import { BadRequestException } from '@nestjs/common'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { IsOptionalEnum } from 'shared/decorators'
import { PaginationDto } from 'shared/dto'
import { CashbackSortTypes, EarningStatusTypes, ReferralTypes } from 'shared/enums'
import { Status, StatusType } from 'shared/types'
import { string } from 'zod'

export class GetUserHistoryDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiPropertyOptional({
		example: 'a',
	})
	searchParam!: string

	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	@ApiPropertyOptional({ example: '2021-10-10', required: false })
	startDate!: string

	@IsOptional()
	@IsString()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	@ApiPropertyOptional({ example: '2021-10-10', required: false })
	endDate!: string

	@IsOptionalEnum(ReferralTypes, { message: 'Invalid sort type' })
	@ApiPropertyOptional({
		enum: ReferralTypes,
		enumName: 'ReferralTypes',
		default: ReferralTypes.Newest,
	})
	sortType?: ReferralTypes = ReferralTypes.Newest

	@IsString()
	@IsOptionalEnum(Status, { message: 'Invalid status' })
	@ApiPropertyOptional({ enum: Status, default: Status.active, type: string })
	status: StatusType = Status.active
}

export class GetUserCashbackDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'a',
	})
	searchParam!: string

	@IsString()
	@IsOptional()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	@ApiProperty({
		default: '2023-10-01',
	})
	startDate!: string

	@IsString()
	@IsOptional()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	@ApiProperty({
		default: '2024-12-31',
	})
	endDate!: string

	@IsOptionalEnum(CashbackSortTypes)
	@ApiPropertyOptional({
		enum: CashbackSortTypes,
		enumName: 'CashbackSortTypes',
		default: CashbackSortTypes.Newest,
	})
	sortType?: CashbackSortTypes

	@IsOptional()
	@Transform(({ value }) => {
		if (value === '') {
			throw new BadRequestException('stores cannot be an empty string')
		}
		return value.split(',').map((val: string) => Number.parseInt(val, 10))
	})
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: '1,2,3',
		type: String,
		required: false,
	})
	stores!: number[]

	@IsOptional()
	@Transform(({ value }) => {
		const statuses = value.split(',')
		if (statuses.includes('pending')) {
			statuses.push('tracked_for_confirm', 'tracked_for_cancel')
		}
		return statuses
	})
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'cancelled,pending,confirmed',
		type: String,
		required: false,
	})
	status!: EarningStatusTypes

	@IsOptional()
	@Transform(({ value }) => {
		if (value === '') {
			throw new BadRequestException('type cannot be an empty string')
		}
		return value.split(',').map((val: string) => val.trim())
	})
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'click,missing,referral',
		type: String,
		required: false,
		description: 'Filter by earnings type (comma-separated values: click,missing,referral,share)',
	})
	type?: ('click' | 'missing' | 'referral' | 'share')[]
}
