import { ApiProperty } from '@nestjs/swagger'

export class UserOverviewResponse {
	@ApiProperty({ type: [Number] })
	totalCashback: number[]

	@ApiProperty({ type: [Number] })
	totalClicks: number[]

	@ApiProperty({ type: [Number] })
	totalOrderAmount: number[]

	@ApiProperty({ type: Number })
	readyToWithdraw: number

	@ApiProperty({ type: Number })
	totalPendingCount: number

	@ApiProperty({ type: Number })
	totalApprovedCount: number

	@ApiProperty({ type: Number })
	totalCancelledCount: number

	@ApiProperty({ type: Number })
	flipkartRewardPoints: number

	@ApiProperty({ type: Number })
	totalCashbackEarned: number

	@ApiProperty({ type: Number })
	totalReferralCommission: number

	@ApiProperty({ type: Number })
	shareAndEarnCashback: number

	@ApiProperty({ type: Number })
	shareAndEarnRewards: number
}
