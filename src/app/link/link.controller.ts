import { Body, Controller, Get, Param, Post, Query, Res } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { Response } from 'express'
import { Auth, ClientInfo, ClientInfoData, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import {
	GenerateLinkDto,
	GenerateMultiLinkDto,
	LinkResponseDto,
	MultiLinkResponseDto,
	PaginationResponseDto,
	UserAnalyticsQueryDto,
	UserAnalyticsResponseDto,
	UserLinkResponseDto,
	UserLinksQueryDto,
} from './dto/link.dto'
import { LinkService } from './link.service'

@ApiTags('Links')
@Controller('links')
export class LinkController {
	constructor(private readonly linkService: LinkService) { }

	@ApiResponse({
		type: LinkResponseDto,
	})
	@Auth()
	@Post('generate')
	async generateLink(
		@Body() generateLinkDto: GenerateLinkDto,
		@User() user: ReqUser
	): Promise<LinkResponseDto> {
		return this.linkService.generateLink(generateLinkDto, user)
	}

	@ApiResponse({
		type: MultiLinkResponseDto,
	})
	@Auth()
	@Post('generate-multi')
	async processMultipleLinks(
		@Body() generateMultiLinkDto: GenerateMultiLinkDto,
		@User() user: ReqUser
	): Promise<MultiLinkResponseDto> {
		return this.linkService.processMultipleLinks(generateMultiLinkDto, user)
	}

	@ApiResponse({
		type: PaginationResponseDto<UserLinkResponseDto>,
	})
	@Auth()
	@Get()
	async getUserLinks(
		@User() user: ReqUser,
		@Query() queryParams: UserLinksQueryDto
	): Promise<PaginationResponseDto<UserLinkResponseDto>> {
		return this.linkService.getUserLinks(user.id, queryParams)
	}

	@ApiResponse({
		type: UserAnalyticsResponseDto,
	})
	@Auth()
	@Get('analytics')
	async getUserAnalytics(
		@User() user: ReqUser,
		@Query() queryParams: UserAnalyticsQueryDto
	): Promise<UserAnalyticsResponseDto> {
		return this.linkService.getUserAnalytics(user.id, queryParams)
	}

	@ApiResponse({
		description: 'Redirect to the original URL',
	})
	@Get(':linkId')
	async getLinkDetails(
		@Param('linkId') linkId: string,
		@Res() res: Response,
		@ClientInfo() clientInfo: ClientInfoData
	) {
		const link = await this.linkService.getLinkDetails(linkId, clientInfo)
		return res.redirect(link)
	}
}
