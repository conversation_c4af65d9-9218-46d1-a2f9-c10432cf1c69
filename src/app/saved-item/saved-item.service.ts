import { ConflictException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { GetGiftCardListResponse } from 'app/gift-card/types/get-gift-card.types'
import { GetAllStoresDto } from 'app/store/dto/get-all-stores.dto'
import { GetAllStoresResponse } from 'app/store/types/get-all-stores.types'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import {
	GiftCardDocument,
	OfferDocument,
	ReqUser,
	SavedItem,
	SavedItemDocument,
	StoreDocument,
	UserDocument,
} from 'shared/entities'
import { buildSavedOfferAggregateQuery } from 'shared/helpers/offer.helper'
import { buildSortQuery } from 'shared/helpers/query-builder'
import { SavedEnum, SavedItemType } from 'shared/types'
import { GetSavedGiftCardsDto, GetSavedOffersDto } from './dto/saved-item.dto'
import { SavedCouponsResponse, SavedDealsResponse } from './types/saved-item.types'

@Injectable()
export class SavedItemService {
	constructor(
		@InjectModel(SavedItem.name)
		private savedItem: Model<SavedItemDocument>,
		private readonly userService: UserService
	) {}

	async saveItem(
		user: UserDocument,
		type: SavedEnum,
		item: OfferDocument | StoreDocument | GiftCardDocument
	) {
		// Check if the item with the same uid, user, and type already exists
		const existingItem = await this.savedItem.findOne({
			user: user._id, // Assuming user._id is the ObjectId of the user
			type,
			itemUid: item.uid,
		})
		if (existingItem) {
			throw new ConflictException(`You have already saved this ${type}.`)
		}
		// Set the refModel value based on the type value
		const refModelKey = this.getSavedModelNameByEnumValue(type)

		const newDoc = {
			user,
			type,
			refModel: refModelKey,
			itemUid: item.uid,
			item,
		}
		const savedDoc = new this.savedItem({
			...newDoc,
		})
		return await savedDoc.save()
	}

	async removeItem(user: UserDocument, type: SavedEnum, itemUid: number): Promise<void> {
		// Find the saved item to remove
		const savedItemToRemove = await this.savedItem.findOne({
			user: user._id,
			type,
			itemUid,
		})

		if (!savedItemToRemove) {
			throw new NotFoundException(`Saved ${type} not found.`)
		}

		// Remove the saved item
		await this.savedItem.deleteOne({ _id: savedItemToRemove._id })
	}

	async getAllSavedDeals(
		queryParams: GetSavedOffersDto,
		userSession: ReqUser
	): Promise<SavedDealsResponse> {
		// Find the user document based on the provided email
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument
		if (!user) {
			throw new NotFoundException('User not found')
		}

		const aggregationPipeline = buildSavedOfferAggregateQuery(queryParams, user._id, 'deal') as any
		const filteredDeals = await this.savedItem.aggregate(aggregationPipeline).exec()

		return {
			deals: filteredDeals[0].documents,
			pagination: {
				page: queryParams.page,
				pageSize: filteredDeals[0].documents?.length,
				total: filteredDeals[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async getAllSavedOfferIds(userSession: ReqUser): Promise<number[]> {
		// Find the user document based on the provided email
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument
		if (!user) {
			throw new NotFoundException('User not found')
		}
		const savedItems = (await this.savedItem.find({
			type: SavedEnum.Offer,
			user: user._id,
		})) as SavedItemDocument[]

		const itemUids = savedItems.map(savedItem => savedItem.itemUid)
		return itemUids
	}

	async getAllSavedCoupons(
		queryParams: GetSavedOffersDto,
		userSession: ReqUser
	): Promise<SavedCouponsResponse> {
		// Find the user document based on the provided email
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument
		if (!user) {
			throw new NotFoundException('User not found')
		}

		const aggregationPipeline = buildSavedOfferAggregateQuery(
			queryParams,
			user._id,
			'coupon'
		) as any
		const filteredCoupons = await this.savedItem.aggregate(aggregationPipeline).exec()

		return {
			coupons: filteredCoupons[0].documents,
			pagination: {
				page: queryParams.page,
				pageSize: filteredCoupons[0].documents?.length,
				total: filteredCoupons[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async getAllSavedStores(
		queryParams: GetAllStoresDto,
		userSession: ReqUser
	): Promise<GetAllStoresResponse> {
		// Find the user document based on the provided email
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument
		if (!user) {
			throw new NotFoundException('User not found')
		}

		//filter items
		const sortQuery = buildSortQuery(
			queryParams.sortType,
			'store.createdAtt',
			'store.name',
			queryParams.sortType === 'highestCbAmount' ? 'store.cashbackAmount' : 'store.cashbackPercent'
		)

		const subCategoriesArray =
			queryParams?.subCategories?.length && queryParams.subCategories.length > 0
				? (queryParams?.subCategories?.split(',').map(Number) ?? [])
				: []

		const searchParam = queryParams?.searchParam ?? ''
		const skip = (queryParams.page - 1) * queryParams.pageSize

		const aggregationPipeline = []

		// Match stage to filter user's deal documents based on user _id
		aggregationPipeline.push({
			$match: {
				type: 'store',
				user: user?._id,
			},
		})

		// Lookup stage to populate "offers" field
		aggregationPipeline.push({
			$lookup: {
				from: 'stores', // Assuming the collection name is 'offers'
				localField: 'item', // Assuming the field containing offer IDs is 'offers'
				foreignField: '_id',
				as: 'store',
			},
		})

		// Unwind the "offers" array to get individual offer documents
		aggregationPipeline.push({
			$unwind: '$store',
		})
		// Match stage to filter offers based on your criteria
		//
		//
		aggregationPipeline.push({
			$match: {
				'store.cashbackAmount': {
					$gte: queryParams.minPercent,
					$lte: queryParams.maxPercent,
				},

				//TO DO -add user type query after the schema has it.

				// Conditionally include subCategories match
				...(subCategoriesArray && subCategoriesArray.length > 0
					? {
							'store.categories.subCategories.uid': {
								$in: subCategoriesArray,
							},
						}
					: {}),

				// Conditionally include title search if searchParam is a non-empty string
				...(searchParam && typeof searchParam === 'string' && searchParam.trim() !== ''
					? { 'store.name': { $regex: searchParam, $options: 'i' } } //i -> case insensitive
					: {}),
			},
		})
		// Add a facet stage to get both documents and total count
		aggregationPipeline.push({
			$facet: {
				// Stage to process documents
				documents: [
					{ $sort: sortQuery },
					{ $skip: skip }, // Skip documents if you're implementing pagination
					{ $limit: queryParams.pageSize }, // Limit documents if you're implementing pagination

					//             select: 'uid bgColor storeOffer logo name',
					{
						// Project stage to shape the output documents
						$project: {
							_id: 0,
							uid: '$store.uid',
							bgColor: '$store.bgColor',
							caption: '$store.storeOffer',
							imageUrl: '$store.logo.secureUrl',
							storeName: '$store.name',
							discount: '$store.cashbackPercent',
						},
					},
					{
						$addFields: {
							saved: true,
						},
					},
				],
				totalCount: [
					{ $count: 'total' }, // Count total documents
				],
			},
		})

		const filteredStores = await this.savedItem.aggregate(aggregationPipeline).exec()

		return {
			stores: filteredStores[0].documents,
			pagination: {
				page: queryParams.page,
				pageSize: filteredStores[0]?.documents?.length,
				total: filteredStores[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async getAllSavedGiftCards(
		queryParams: GetSavedGiftCardsDto,
		userSession: ReqUser
	): Promise<GetGiftCardListResponse> {
		// Find the user document based on the provided email
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument
		if (!user) {
			throw new NotFoundException('User not found')
		}

		//filter items
		const sortQuery = buildSortQuery(
			queryParams.sortType,
			'giftCard.createdAt',
			'giftCard.name',
			'giftCard.discountGetting'
		)

		const subCategoriesArray = queryParams.subCategoriesArray
		const searchParam = queryParams?.searchParam ?? ''

		const skip = (queryParams.page - 1) * queryParams.pageSize

		const aggregationPipeline = []

		// Match stage to filter user's deal documents based on user _id
		aggregationPipeline.push({
			$match: {
				type: 'giftCard',
				user: user?._id,
			},
		})

		// Lookup stage to populate "offers" field
		aggregationPipeline.push({
			$lookup: {
				from: 'giftcards', // Assuming the collection name is 'offers'
				localField: 'item', // Assuming the field containing offer IDs is 'offers'
				foreignField: '_id',
				as: 'giftCard',
			},
		})

		// Unwind the "offers" array to get individual offer documents
		aggregationPipeline.push({
			$unwind: '$giftCard',
		})
		// Match stage to filter offers based on your criteria
		aggregationPipeline.push({
			$match: {
				//TO DO -add user type query after the schema has it.

				// Conditionally include subCategories match
				...(subCategoriesArray && subCategoriesArray.length > 0
					? {
							'giftCard.categories.subCategories.uid': {
								$in: subCategoriesArray,
							},
						}
					: {}),

				// Conditionally include title search if searchParam is a non-empty string
				...(searchParam && typeof searchParam === 'string' && searchParam.trim() !== ''
					? { 'giftCard.name': { $regex: searchParam, $options: 'i' } } //i -> case insensitive
					: {}),
			},
		})
		// Add a facet stage to get both documents and total count
		aggregationPipeline.push({
			$facet: {
				// Stage to process documents
				documents: [
					{ $skip: skip }, // Skip documents if you're implementing pagination
					{ $limit: queryParams.pageSize }, // Limit documents if you're implementing pagination
					{ $sort: sortQuery },

					//             select: 'uid bgColor storeOffer logo name',
					{
						// Project stage to shape the output documents
						$project: {
							_id: 0,
							uid: '$giftCard.uid',
							caption: {
								$concat: ['Up to ', { $toString: '$giftCard.cashbackGiving' }, '% cashback'],
							},
							// '$giftCard.cashbackGiving',
							imageUrl: '$giftCard.image.secureUrl',
							name: '$giftCard.name',
						},
					},
					{
						$addFields: {
							saved: true,
						},
					},
				],
				totalCount: [
					{ $count: 'total' }, // Count total documents
				],
			},
		})

		const filteredGiftCards = await this.savedItem.aggregate(aggregationPipeline).exec()

		return {
			giftCards: filteredGiftCards[0]?.documents,
			pagination: {
				page: queryParams.page,
				pageSize: filteredGiftCards[0]?.documents?.length,
				total: filteredGiftCards[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async checkIfSaved(type: string, itemUid: number, user: Types.ObjectId) {
		const savedItem = (await this.savedItem.findOne({
			type,
			itemUid,
			user,
		})) as SavedItemDocument

		if (savedItem) {
			return true
		}
		return false
	}

	async findSavedItemsByUserAndType(types: SavedEnum[], user: Types.ObjectId) {
		const savedItems = (await this.savedItem.find({
			type: { $in: types },
			user,
		})) as SavedItemDocument[]

		return savedItems
	}

	getSavedModelNameByEnumValue(value: string): SavedItemType | undefined {
		const entries = Object.entries(SavedEnum)
		for (const [key, val] of entries) {
			if (val === value) {
				// If the value is "deal" or "coupon", return "Offer" instead
				if (value === 'offer') {
					return 'Offer' as SavedItemType
				}
				return key as SavedItemType
			}
		}
		return
	}

	//
	// async getSavingItemInstance(itemType: string, uid: number) {
	//     switch (itemType) {
	//
	//         case 'offer':
	//             return await this.offerService.getOfferByUid(uid);
	//
	//         case 'store':
	//             return await this.storeService.getStoreByUid(uid);
	//
	//         case 'giftCard':
	//             return await this.giftCardService.getGiftCardByUid(uid);
	//
	//         default:
	//             throw new Error(`Unsupported item type: ${itemType} `);
	//     }
	// }
	//
}
