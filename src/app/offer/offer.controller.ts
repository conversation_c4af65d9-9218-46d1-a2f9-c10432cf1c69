import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { SaveItemResponse } from 'app/saved-item/types/saved-item.types'
import { Auth, AuthOptional, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { CouponsAndDealsDto, OngoingSalesDto, RemoveOfferDto, SaveOfferDto } from './dto/offer.dto'
import { OfferService } from './offer.service'
import {
	DealAndCouponsResponse,
	GetOfferByIdResponse,
	GetSimilarOffers,
	OngoingOffersResponse,
} from './types/offer.types'

@ApiTags('Offers')
@Controller('offers')
export class OfferController {
	constructor(private readonly offerService: OfferService) {}

	@ApiResponse({
		type: DealAndCouponsResponse,
	})
	@AuthOptional()
	@Get('/deals-and-coupons')
	async getAllCategories(
		@Query() queryParams: CouponsAndDealsDto,
		@User() user: ReqUser
	): Promise<DealAndCouponsResponse> {
		return this.offerService.getAllOffers(queryParams, user)
	}

	@ApiResponse({
		type: OngoingOffersResponse,
	})
	@AuthOptional()
	@Get('/ongoing-offers')
	async getOngoingOffers(
		@Query() queryParams: OngoingSalesDto,
		@User() user: ReqUser
	): Promise<OngoingOffersResponse> {
		return this.offerService.getOngoingOffers(queryParams, user)
	}

	@ApiResponse({
		type: GetOfferByIdResponse,
	})
	@AuthOptional()
	@Get('offer:uid')
	async getOfferById(@Param('uid') uid: number, @User() user: ReqUser) {
		return await this.offerService.getOfferAndSimilarOffers(uid, user)
	}

	@ApiResponse({
		type: GetSimilarOffers,
	})
	@AuthOptional()
	@Get('offer/title/:title')
	async getOfferByTitle(@Param('title') title: string, @User() user: ReqUser) {
		return await this.offerService.getOffersByTitleAndSimilarOffers(title, user)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/save')
	async saveItem(@Body() createSaveItem: SaveOfferDto, @User() user: ReqUser) {
		return await this.offerService.saveOffer(createSaveItem, user)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/remove')
	async removeSavedItem(@Body() removeSavedItem: RemoveOfferDto, @User() user: ReqUser) {
		return await this.offerService.removeSavedOffer(user, removeSavedItem)
	}
}
