import { addAliases } from 'module-alias'

// Register the path mappings
addAliases({
	'@app': `${__dirname}/app`,
	'@seed': `${__dirname}/seed`,
	'@config': `${__dirname}/config`,
	'@shared': `${__dirname}/shared`,
	'@functions': `${__dirname}/functions`,
	app: `${__dirname}/app`,
	seed: `${__dirname}/seed`,
	config: `${__dirname}/config`,
	shared: `${__dirname}/shared`,
	functions: `${__dirname}/functions`,
})

import fs from 'node:fs'
import { customCss, env } from '@config'
import { Logger, ValidationPipe } from '@nestjs/common'
// declare global {
// 	namespace nodeJs {
// 		interface Global {
// 			csrfToken: string
// 			[x: string]: any
// 		}
// 	}
// }
// declare const global: nodeJs.Global
import { NestFactory } from '@nestjs/core'
import { NestExpressApplication } from '@nestjs/platform-express'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { apiReference } from '@scalar/nestjs-api-reference'
import compression from 'compression'
import RedisStore from 'connect-redis'
import cookieParser from 'cookie-parser'
import session from 'express-session'
import helmet from 'helmet'
import { CsrfFilter, nestCsrf } from 'ncsrf'
import passport from 'passport'
import { createClient } from 'redis'
// biome-ignore lint/performance/noNamespaceImport: importing for swagger-stats
import * as swaggerStats from 'swagger-stats'
import { AppModule } from './app/app.module'

async function bootstrap() {
	const logger = new Logger('ICB API')
	let app: NestExpressApplication

	if (env.APP.nodeEnv === 'testing') {
		const httpsOptions = {
			key: fs.readFileSync('./secrets/key.pem'),
			cert: fs.readFileSync('./secrets/cert.pem'),
		}
		app = await NestFactory.create<NestExpressApplication>(AppModule, {
			httpsOptions,
		})
	} else {
		app = await NestFactory.create<NestExpressApplication>(AppModule)
	}

	app.use(cookieParser())

	// Enable ETag support
	app.set('etag', 'strong') // or 'weak' for weak ETags

	app.useGlobalPipes(new ValidationPipe({ transform: true }))
	// app.useGlobalFilters(new HttpExceptionFilter());
	app.useGlobalFilters(new CsrfFilter())
	const config = new DocumentBuilder()
		.setTitle('ICB API')
		.setDescription('ICB API for frontend')
		.setVersion('1.0')
		.addCookieAuth('accessToken')
		.build()

	app.use(compression())
	const document = SwaggerModule.createDocument(app, config, {
		deepScanRoutes: true,
	})

	const redisClient = createClient({
		url: env.REDIS.url,
		pingInterval: 2000,
	})
	redisClient.connect().catch(console.error)
	const redisStore = new RedisStore({
		client: redisClient,
		prefix: 'AUTH:',
	})

	app.enableCors({
		origin: env.APP.origin,
		credentials: true,
	})

	if (env.APP.nodeEnv !== 'testing') {
		app.set('trust proxy', 1)
	}
	app.use(
		session({
			store: redisStore,
			secret: env.SESSION.secret,
			name: 'accessToken',
			resave: false,
			saveUninitialized: false,
			cookie: {
				httpOnly: env.APP.nodeEnv !== 'testing',
				maxAge: 1000 * 60 * 60 * 24 * 30, // 30 day
				secure: env.APP.nodeEnv !== 'testing',
				signed: true,
				sameSite: env.APP.nodeEnv === 'testing' ? false : 'none',
			},
		})
	)

	app.use(passport.initialize({}))
	app.use(passport.session())
	app.use(
		nestCsrf({
			// signed: true,
		})
	)

	if (env.APP.nodeEnv === 'production') {
		logger.log('Production mode')
		app.use(helmet({}))
	}

	if (env.APP.nodeEnv !== 'production') {
		// Assuming CSRF token is available in a cookie, fetch it from there
		// app.use(
		// 	// biome-ignore lint/suspicious/noExplicitAny: required for external API
		// 	(req: any, res: any, next: () => void) => {
		// 		console.log('🚀 ~ bootstrap ~ req.cookies:', req.csrfToken())
		// 		const csrfToken = req.csrfToken() // Adjust based on how your CSRF token is stored
		// 		if (csrfToken) {
		// 			// Make the CSRF token available to Swagger UI for use in requests
		// 			// This could be done via a global variable, or customizing Swagger UI to read from a cookie directly
		// 			// For example, setting a global variable (not directly supported, but as an illustration)
		// 			global.csrfToken = csrfToken as string
		// 		}
		// 		next()
		// 	},
		// )
		logger.log('Development mode')
		app.use(
			'/api',
			apiReference({
				showSidebar: true,
				darkMode: true,
				searchHotKey: 's',
				layout: 'modern',
				theme: 'purple',
				cdn: 'https://cdn.jsdelivr.net/npm/@scalar/api-reference',
				spec: {
					content: document,
				},
			})
		)
		logger.log(`Swagger api at http://localhost:${env.APP.port}/api`)
	}
	SwaggerModule.setup('api-legacy-docs', app, document, {
		customSiteTitle: 'ICB API',
		customCss,
	})

	app.use(
		swaggerStats.getMiddleware({
			name: 'icb api stats',
			version: '1.0.0',
			uriPath: '/stats',
			authentication: true,
			apdexThreshold: 30,
			swaggerSpec: document,
			sessionMaxAge: 86_400,
			swaggerOnly: true,
			onAuthenticate(_req, username: string, password: string) {
				return username === env.SWAGGER_STATS.username && password === env.SWAGGER_STATS.password
			},
		})
	)

	await app.listen(env.APP.port)
	logger.log(`Server is Connected at https://localhost:${env.APP.port}`)
	logger.log(`Swagger Stats at https://localhost:${env.APP.port}/stats`)
	logger.log(`Swagger Scalar Docs at https://localhost:${env.APP.port}/api`)
	logger.log(`Swagger Legacy Docs at https://localhost:${env.APP.port}/api-legacy-docs`)
	logger.verbose('Api is listening...version 1.0.0')
}
bootstrap()
